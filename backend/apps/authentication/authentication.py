import jwt
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class KeycloakAuthentication(BaseAuthentication):
    """Keycloak JWT Token认证"""
    
    def authenticate(self, request):
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
            
        token = auth_header.split(' ')[1]
        
        try:
            # 验证token并获取用户信息
            user_info = self.verify_token(token)
            if not user_info:
                return None
                
            # 获取或创建用户
            user = self.get_or_create_user(user_info)
            return (user, token)
            
        except Exception as e:
            logger.error(f"Keycloak authentication failed: {str(e)}")
            raise AuthenticationFailed('Invalid token')
    
    def verify_token(self, token):
        """验证Keycloak token"""
        try:
            # 首先尝试从缓存获取公钥
            public_key = cache.get('keycloak_public_key')
            if not public_key:
                public_key = self.get_keycloak_public_key()
                cache.set('keycloak_public_key', public_key, 3600)  # 缓存1小时
            
            # 解码JWT token
            decoded_token = jwt.decode(
                token,
                public_key,
                algorithms=['RS256'],
                audience='account',
                options={"verify_exp": True}
            )
            
            return decoded_token
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token: {str(e)}")
            raise AuthenticationFailed('Invalid token')
    
    def get_keycloak_public_key(self):
        """获取Keycloak公钥"""
        try:
            keycloak_config = settings.KEYCLOAK_CONFIG
            well_known_url = f"{keycloak_config['KEYCLOAK_SERVER_URL']}realms/{keycloak_config['KEYCLOAK_REALM']}/.well-known/openid-configuration"
            
            response = requests.get(well_known_url, timeout=10)
            response.raise_for_status()
            
            jwks_uri = response.json()['jwks_uri']
            jwks_response = requests.get(jwks_uri, timeout=10)
            jwks_response.raise_for_status()
            
            jwks = jwks_response.json()
            
            # 获取第一个密钥（通常只有一个）
            key_data = jwks['keys'][0]
            
            # 构建公钥
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.asymmetric import rsa
            import base64
            
            n = base64.urlsafe_b64decode(key_data['n'] + '==')
            e = base64.urlsafe_b64decode(key_data['e'] + '==')
            
            n_int = int.from_bytes(n, 'big')
            e_int = int.from_bytes(e, 'big')
            
            public_key = rsa.RSAPublicNumbers(e_int, n_int).public_key()
            
            return public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
        except Exception as e:
            logger.error(f"Failed to get Keycloak public key: {str(e)}")
            raise AuthenticationFailed('Failed to verify token')
    
    def get_or_create_user(self, user_info):
        """根据Keycloak用户信息获取或创建用户"""
        keycloak_id = user_info.get('sub')
        username = user_info.get('preferred_username')
        email = user_info.get('email', '')
        
        try:
            # 尝试通过keycloak_id查找用户
            user = User.objects.get(keycloak_id=keycloak_id)
            
            # 更新用户信息
            user.username = username
            user.email = email
            user.first_name = user_info.get('given_name', '')
            user.last_name = user_info.get('family_name', '')
            user.email_verified = user_info.get('email_verified', False)
            user.save()
            
        except User.DoesNotExist:
            # 创建新用户
            user = User.objects.create(
                username=username,
                email=email,
                keycloak_id=keycloak_id,
                preferred_username=username,
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                email_verified=user_info.get('email_verified', False),
                is_active=True
            )
            
        return user
