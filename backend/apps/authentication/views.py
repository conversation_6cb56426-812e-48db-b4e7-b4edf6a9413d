from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
import requests
import logging

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """健康检查接口"""
    return Response({
        'status': 'ok',
        'message': 'Authentication service is running'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """获取当前用户信息"""
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'preferred_username': user.preferred_username,
        'email_verified': user.email_verified,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def keycloak_config(request):
    """获取Keycloak前端配置"""
    keycloak_config = settings.KEYCLOAK_CONFIG
    return Response({
        'url': keycloak_config['KEYCLOAK_SERVER_URL'],
        'realm': keycloak_config['KEYCLOAK_REALM'],
        'clientId': 'front',  # 前端使用的client ID
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout(request):
    """登出接口"""
    try:
        # 这里可以添加登出逻辑，比如撤销token等
        return Response({
            'message': 'Logout successful'
        })
    except Exception as e:
        logger.error(f"Logout failed: {str(e)}")
        return Response({
            'error': 'Logout failed'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
