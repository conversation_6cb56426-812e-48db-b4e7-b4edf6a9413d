from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
import logging

logger = logging.getLogger(__name__)


class KeycloakAuthenticationMiddleware(MiddlewareMixin):
    """Keycloak认证中间件"""
    
    def process_request(self, request):
        """处理请求"""
        # 跳过不需要认证的路径
        skip_paths = [
            '/admin/',
            '/api/auth/health/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
            
        return None
    
    def process_exception(self, request, exception):
        """处理异常"""
        if hasattr(exception, 'status_code') and exception.status_code == 401:
            return JsonResponse({
                'error': 'Authentication required',
                'message': str(exception)
            }, status=401)
            
        return None
