from django.db import models
from django.contrib.auth.models import AbstractUser


class User(AbstractUser):
    """扩展用户模型"""
    keycloak_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    preferred_username = models.CharField(max_length=255, null=True, blank=True)
    email_verified = models.BooleanField(default=False)
    
    def __str__(self):
        return self.username or self.preferred_username or self.email
