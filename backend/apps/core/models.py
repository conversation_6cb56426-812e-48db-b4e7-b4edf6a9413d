from django.db import models


class SystemInfo(models.Model):
    """系统信息模型"""
    name = models.CharField(max_length=255, default='NVH数据管理系统')
    version = models.CharField(max_length=50, default='1.0.0')
    description = models.TextField(default='现代化的NVH数据管理系统')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = '系统信息'
        verbose_name_plural = '系统信息'
    
    def __str__(self):
        return f"{self.name} v{self.version}"
