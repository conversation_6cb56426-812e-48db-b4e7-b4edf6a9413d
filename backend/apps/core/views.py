from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SystemInfo
from django.utils import timezone


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard(request):
    """首页仪表板数据"""
    user = request.user
    
    # 获取系统信息
    try:
        system_info = SystemInfo.objects.first()
        if not system_info:
            system_info = SystemInfo.objects.create()
    except Exception:
        system_info = SystemInfo()
    
    return Response({
        'user': {
            'name': user.get_full_name() or user.username,
            'username': user.username,
            'email': user.email,
        },
        'system': {
            'name': system_info.name,
            'version': system_info.version,
            'description': system_info.description,
        },
        'stats': {
            'current_time': timezone.now(),
            'total_users': 1,  # 暂时硬编码
            'system_status': 'running',
        },
        'menu_items': [
            {
                'id': 'home',
                'name': '首页',
                'icon': 'House',
                'path': '/',
                'active': True
            },
            {
                'id': 'business',
                'name': '业务中心',
                'icon': 'OfficeBuilding',
                'path': '/business',
                'active': False
            },
            {
                'id': 'permission',
                'name': '权限管理',
                'icon': 'Lock',
                'path': '/permission',
                'active': False
            },
            {
                'id': 'development',
                'name': '待开发',
                'icon': 'Tools',
                'path': '/development',
                'active': False
            }
        ]
    })
