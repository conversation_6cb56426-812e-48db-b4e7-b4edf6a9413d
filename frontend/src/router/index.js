import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/views/Layout.vue'
import Home from '@/views/Home.vue'
import Business from '@/views/Business.vue'
import Permission from '@/views/Permission.vue'
import Development from '@/views/Development.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: Home,
        meta: {
          title: '首页',
          icon: 'House'
        }
      },
      {
        path: '/business',
        name: 'Business',
        component: Business,
        meta: {
          title: '业务中心',
          icon: 'OfficeBuilding'
        }
      },
      {
        path: '/permission',
        name: 'Permission',
        component: Permission,
        meta: {
          title: '权限管理',
          icon: 'Lock'
        }
      },
      {
        path: '/development',
        name: 'Development',
        component: Development,
        meta: {
          title: '待开发',
          icon: 'Tools'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
