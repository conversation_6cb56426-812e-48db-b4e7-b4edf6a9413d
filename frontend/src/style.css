/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
}

#app {
  height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 200px;
  background: #001529;
  overflow-y: auto;
}

.layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-tabs {
  height: 40px;
  background: #f5f5f5;
  border-bottom: 1px solid #e6e6e6;
}

.layout-page {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f0f2f5;
}

/* 菜单样式 */
.el-menu {
  border-right: none !important;
}

.el-menu--dark .el-menu-item {
  color: rgba(255, 255, 255, 0.65);
}

.el-menu--dark .el-menu-item:hover {
  background-color: #1890ff !important;
  color: #fff;
}

.el-menu--dark .el-menu-item.is-active {
  background-color: #1890ff !important;
  color: #fff;
}

/* 标签页样式 */
.tabs-container {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.tab-item {
  height: 28px;
  line-height: 28px;
  padding: 0 12px;
  margin-right: 4px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
}

.tab-item.active {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.tab-item .close-icon {
  margin-left: 6px;
  font-size: 10px;
}

/* 卡片样式 */
.dashboard-card {
  margin-bottom: 20px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card .el-card__body {
  padding: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}
