import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { initKeycloak } from './utils/keycloak'
import './style.css'

// 初始化Keycloak
initKeycloak().then((keycloak) => {
  const app = createApp(App)
  
  // 注册Element Plus图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  app.use(createPinia())
  app.use(router)
  app.use(ElementPlus)
  
  // 将keycloak实例挂载到全局属性
  app.config.globalProperties.$keycloak = keycloak
  app.provide('keycloak', keycloak)
  
  app.mount('#app')
}).catch(error => {
  console.error('Failed to initialize Keycloak:', error)
})
