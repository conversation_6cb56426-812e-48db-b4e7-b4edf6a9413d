import Keycloak from 'keycloak-js'

let keycloakInstance = null

export const initKeycloak = () => {
  return new Promise((resolve, reject) => {
    const initOptions = {
      url: 'https://account-test.sgmw.com.cn/auth/',
      realm: 'demo',
      clientId: 'front',
      onLoad: 'login-required'
    }

    keycloakInstance = new Keycloak(initOptions)

    keycloakInstance.init({ 
      onLoad: initOptions.onLoad, 
      checkLoginIframe: false,
      silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html'
    }).then((authenticated) => {
      if (!authenticated) {
        console.log('User not authenticated')
        window.location.reload()
      } else {
        console.log('User authenticated successfully')
        
        // 设置token刷新定时器
        setInterval(() => {
          keycloakInstance.updateToken(70).then((refreshed) => {
            if (refreshed) {
              console.log('Token refreshed')
            } else {
              console.log('Token not refreshed, valid for ' 
                + Math.round(keycloakInstance.tokenParsed.exp + keycloakInstance.timeSkew - new Date().getTime() / 1000) + ' seconds')
            }
          }).catch(error => {
            console.error('Failed to refresh token', error)
          })
        }, 60000)
        
        resolve(keycloakInstance)
      }
    }).catch(error => {
      console.error('Keycloak initialization failed', error)
      reject(error)
    })
  })
}

export const getKeycloak = () => {
  return keycloakInstance
}

export const getToken = () => {
  return keycloakInstance?.token
}

export const getUserInfo = () => {
  if (!keycloakInstance?.tokenParsed) {
    return null
  }
  
  return {
    id: keycloakInstance.tokenParsed.sub,
    username: keycloakInstance.tokenParsed.preferred_username,
    email: keycloakInstance.tokenParsed.email,
    firstName: keycloakInstance.tokenParsed.given_name,
    lastName: keycloakInstance.tokenParsed.family_name,
    fullName: keycloakInstance.tokenParsed.name,
  }
}

export const logout = () => {
  if (keycloakInstance) {
    keycloakInstance.logout({
      redirectUri: window.location.origin
    })
  }
}
