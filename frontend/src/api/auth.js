import request from '@/utils/request'

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/user/',
    method: 'get'
  })
}

// 获取Keycloak配置
export function getKeycloakConfig() {
  return request({
    url: '/auth/config/',
    method: 'get'
  })
}

// 登出
export function logout() {
  return request({
    url: '/auth/logout/',
    method: 'post'
  })
}

// 健康检查
export function healthCheck() {
  return request({
    url: '/auth/health/',
    method: 'get'
  })
}
