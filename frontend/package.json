{"name": "nvh-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.2", "keycloak-js": "^23.0.1", "pinia": "^2.1.7"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "sass": "^1.69.5"}}